<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS.js 播放器演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(45, 45, 45, 0.8);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            color: #4caf50;
            margin-bottom: 10px;
            font-size: 28px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            color: #ccc;
            font-size: 16px;
        }

        .controls {
            background: rgba(45, 45, 45, 0.9);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #4caf50;
        }

        .control-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #555;
            border-radius: 8px;
            background: #3d3d3d;
            color: #fff;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .control-group input:focus {
            outline: none;
            border-color: #4caf50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #666 0%, #555 100%);
            box-shadow: 0 4px 15px rgba(102, 102, 102, 0.3);
        }

        .btn-secondary:hover {
            box-shadow: 0 6px 20px rgba(102, 102, 102, 0.4);
        }

        .video-container {
            background: rgba(45, 45, 45, 0.9);
            border-radius: 15px;
            overflow: hidden;
            position: relative;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(76, 175, 80, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .video-wrapper {
            position: relative;
            width: 100%;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: #000;
        }

        .video-player {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
        }

        .video-info {
            padding: 15px;
            background: rgba(61, 61, 61, 0.8);
            border-top: 1px solid #555;
        }

        .status {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 25px;
            border-radius: 8px;
            font-size: 14px;
            text-align: center;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .loading {
            color: #4caf50;
        }

        .error {
            color: #f44336;
        }

        .success {
            color: #4caf50;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .info-item {
            background: rgba(76, 175, 80, 0.1);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .info-item strong {
            color: #4caf50;
        }

        @media (max-width: 768px) {
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .header p {
                font-size: 14px;
            }
        }

        .loading-spinner {
            border: 3px solid rgba(76, 175, 80, 0.3);
            border-top: 3px solid #4caf50;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 HLS.js 播放器演示</h1>
            <p>使用 HLS.js 库播放 HTTP Live Streaming (HLS) 视频流</p>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="hlsUrl">HLS 流地址:</label>
                <input type="text" id="hlsUrl" 
                       value="http://192.168.70.43:8999/live/test_sun/stream1/playlist.m3u8" 
                       placeholder="请输入 HLS 流地址 (例如: .m3u8 文件)">
            </div>
            
            <div class="button-group">
                <button class="btn" onclick="loadStream()">🎯 加载流</button>
                <button class="btn" onclick="playVideo()">▶️ 播放</button>
                <button class="btn" onclick="pauseVideo()">⏸️ 暂停</button>
                <button class="btn btn-secondary" onclick="stopVideo()">⏹️ 停止</button>
                <button class="btn btn-secondary" onclick="toggleMute()">🔇 静音/取消静音</button>
            </div>
        </div>

        <div class="video-container">
            <div class="video-wrapper">
                <video id="video" class="video-player" controls muted></video>
                <div class="status" id="status">
                    <div class="loading-spinner"></div>
                    等待加载流...
                </div>
            </div>
            
            <div class="video-info">
                <h3 style="color: #4caf50; margin-bottom: 10px;">📊 流信息</h3>
                <div class="info-grid" id="streamInfo">
                    <div class="info-item">
                        <strong>状态:</strong> <span id="streamStatus">未连接</span>
                    </div>
                    <div class="info-item">
                        <strong>质量等级:</strong> <span id="qualityLevel">-</span>
                    </div>
                    <div class="info-item">
                        <strong>缓冲长度:</strong> <span id="bufferLength">-</span>
                    </div>
                    <div class="info-item">
                        <strong>当前时间:</strong> <span id="currentTime">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入 HLS.js 库 -->
    <script src="hls.min.js"></script>
    
    <script>
        class HLSPlayer {
            constructor() {
                this.video = document.getElementById('video');
                this.hls = null;
                this.isPlaying = false;
                this.init();
            }

            init() {
                // 检查 HLS.js 是否可用
                if (!Hls.isSupported()) {
                    this.updateStatus('您的浏览器不支持 HLS.js', 'error');
                    console.error('HLS.js is not supported');
                    return;
                }

                this.setupVideoEvents();
                this.startInfoUpdater();
            }

            setupVideoEvents() {
                this.video.addEventListener('loadstart', () => {
                    this.updateStatus('开始加载...', 'loading');
                });

                this.video.addEventListener('loadedmetadata', () => {
                    this.updateStatus('元数据已加载', 'loading');
                });

                this.video.addEventListener('canplay', () => {
                    this.updateStatus('可以播放', 'success');
                    setTimeout(() => {
                        document.getElementById('status').style.display = 'none';
                    }, 2000);
                });

                this.video.addEventListener('playing', () => {
                    this.isPlaying = true;
                    document.getElementById('streamStatus').textContent = '播放中';
                    document.getElementById('status').style.display = 'none';
                });

                this.video.addEventListener('pause', () => {
                    this.isPlaying = false;
                    document.getElementById('streamStatus').textContent = '已暂停';
                });

                this.video.addEventListener('error', (e) => {
                    this.updateStatus('视频播放错误: ' + e.message, 'error');
                    document.getElementById('streamStatus').textContent = '错误';
                });

                this.video.addEventListener('waiting', () => {
                    this.updateStatus('缓冲中...', 'loading');
                });
            }

            loadStream(url) {
                if (!url || !url.trim()) {
                    this.updateStatus('请输入有效的 HLS 流地址', 'error');
                    return;
                }

                this.updateStatus('正在连接流...', 'loading');
                
                // 销毁之前的 HLS 实例
                if (this.hls) {
                    this.hls.destroy();
                }

                // 创建新的 HLS 实例
                this.hls = new Hls({
                    debug: true,
                    enableWorker: true,
                    lowLatencyMode: true,
                });

                // 设置 HLS 事件监听器
                this.setupHLSEvents();

                // 加载流
                this.hls.loadSource(url);
                this.hls.attachMedia(this.video);

                console.log('Loading HLS stream:', url);
            }

            setupHLSEvents() {
                this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
                    console.log('Manifest parsed, found levels:', this.hls.levels);
                    this.updateStatus('流清单解析成功', 'success');
                    document.getElementById('streamStatus').textContent = '已连接';
                    
                    // 更新质量等级信息
                    const levels = this.hls.levels;
                    if (levels.length > 0) {
                        const levelInfo = levels.map(level => `${level.height}p`).join(', ');
                        document.getElementById('qualityLevel').textContent = levelInfo;
                    }
                });

                this.hls.on(Hls.Events.ERROR, (event, data) => {
                    console.error('HLS Error:', data);
                    if (data.fatal) {
                        switch (data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                this.updateStatus('网络错误，尝试恢复...', 'error');
                                this.hls.startLoad();
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                this.updateStatus('媒体错误，尝试恢复...', 'error');
                                this.hls.recoverMediaError();
                                break;
                            default:
                                this.updateStatus('致命错误，无法恢复', 'error');
                                this.hls.destroy();
                                break;
                        }
                    }
                });

                this.hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
                    console.log('Level switched to:', data.level);
                    const level = this.hls.levels[data.level];
                    if (level) {
                        document.getElementById('qualityLevel').textContent = `${level.height}p (当前)`;
                    }
                });
            }

            updateStatus(message, type = '') {
                const statusEl = document.getElementById('status');
                const spinner = statusEl.querySelector('.loading-spinner');
                
                if (type === 'loading') {
                    spinner.style.display = 'block';
                } else {
                    spinner.style.display = 'none';
                }
                
                statusEl.innerHTML = (type === 'loading' ? '<div class="loading-spinner"></div>' : '') + message;
                statusEl.className = `status ${type}`;
                statusEl.style.display = 'block';
            }

            startInfoUpdater() {
                setInterval(() => {
                    if (this.video && !this.video.paused) {
                        // 更新当前时间
                        const currentTime = this.video.currentTime;
                        const duration = this.video.duration;
                        if (!isNaN(currentTime) && !isNaN(duration)) {
                            const timeStr = `${this.formatTime(currentTime)} / ${this.formatTime(duration)}`;
                            document.getElementById('currentTime').textContent = timeStr;
                        }

                        // 更新缓冲长度
                        if (this.hls) {
                            const bufferLength = this.hls.media ? this.hls.media.buffered.length : 0;
                            document.getElementById('bufferLength').textContent = bufferLength + ' 段';
                        }
                    }
                }, 1000);
            }

            formatTime(seconds) {
                const mins = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }

            play() {
                if (this.video) {
                    this.video.play().catch(e => {
                        console.error('Play failed:', e);
                        this.updateStatus('播放失败: ' + e.message, 'error');
                    });
                }
            }

            pause() {
                if (this.video) {
                    this.video.pause();
                }
            }

            stop() {
                if (this.video) {
                    this.video.pause();
                    this.video.currentTime = 0;
                }
                if (this.hls) {
                    this.hls.destroy();
                    this.hls = null;
                }
                document.getElementById('streamStatus').textContent = '已停止';
                this.updateStatus('播放已停止', '');
            }

            toggleMute() {
                if (this.video) {
                    this.video.muted = !this.video.muted;
                }
            }
        }

        // 创建播放器实例
        const player = new HLSPlayer();

        // 全局函数
        function loadStream() {
            const url = document.getElementById('hlsUrl').value;
            player.loadStream(url);
        }

        function playVideo() {
            player.play();
        }

        function pauseVideo() {
            player.pause();
        }

        function stopVideo() {
            player.stop();
        }

        function toggleMute() {
            player.toggleMute();
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (player.hls) {
                player.hls.destroy();
            }
        });
    </script>
</body>
</html>
