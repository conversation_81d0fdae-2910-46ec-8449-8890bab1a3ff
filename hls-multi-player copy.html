<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HTML5多路视频播放器</title>
    <!-- Native HTML5 video streaming - no external libraries needed -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: #1a1a1a;
        color: #fff;
        padding: 0;
        margin: 0;
        overflow: auto;
      }

      .container {
        /* max-width: 1400px; */
        margin: 0 auto;
        /* padding: 20px; */
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
      }

      .header h1 {
        color: #4caf50;
        margin-bottom: 10px;
      }

      .settings-button {
        position: fixed;
        top: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        background: #4caf50;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        z-index: 1000;
        opacity: 0;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }

      .settings-button:hover {
        background: #45a049;
        transform: scale(1.1);
      }

      .settings-trigger {
        position: fixed;
        top: 0;
        left: 0;
        width: 100px;
        height: 100px;
        z-index: 999;
      }

      .settings-trigger:hover + .settings-button,
      .settings-button:hover {
        opacity: 1;
      }

      .controls {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #2d2d2d;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        z-index: 1001;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        display: none;
      }

      .controls.show {
        display: block;
      }

      .controls-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #555;
      }

      .controls-header h2 {
        margin: 0;
        color: #4caf50;
      }

      .close-button {
        background: #666;
        border: none;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: background 0.3s;
      }

      .close-button:hover {
        background: #777;
      }

      .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
      }

      .overlay.show {
        display: block;
      }

      .control-group {
        margin-bottom: 15px;
      }

      .control-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
      }

      .control-group select,
      .control-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #555;
        border-radius: 5px;
        background: #3d3d3d;
        color: #fff;
      }

      .url-inputs {
        display: grid;
        gap: 10px;
        margin-top: 15px;
      }

      .url-input-group {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .url-input-group label {
        min-width: 80px;
        margin-bottom: 0;
      }

      .url-input-group input {
        flex: 1;
      }

      .btn {
        background: #4caf50;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
        transition: background 0.3s;
      }

      .btn:hover {
        background: #45a049;
      }

      .btn-secondary {
        background: #666;
      }

      .btn-secondary:hover {
        background: #777;
      }

      .video-grid {
        display: grid;
        gap: 0px;
        margin-top: 20px;
      }

      .video-grid.grid-1 {
        grid-template-columns: 1fr;
      }

      .video-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr);
      }

      .video-grid.grid-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      .video-grid.grid-4 {
        grid-template-columns: repeat(2, 1fr);
      }

      .video-grid.grid-5,
      .video-grid.grid-6 {
        grid-template-columns: repeat(3, 1fr);
      }

      .video-container {
        background: #2d2d2d;
        border-radius: 0px;
        overflow: hidden;
        position: relative;
      }

      .video-header {
        background: #3d3d3d;
        padding: 10px;
        font-size: 14px;
        font-weight: bold;
      }

      .video-wrapper {
        position: relative;
        width: 100%;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
      }

      .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #000;
      }

      .video-status {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        padding: 10px 20px;
        border-radius: 5px;
        font-size: 14px;
      }

      .loading {
        color: #4caf50;
      }

      .error {
        color: #f44336;
      }

      @media (max-width: 768px) {
        .video-grid.grid-2,
        .video-grid.grid-3,
        .video-grid.grid-4,
        .video-grid.grid-5,
        .video-grid.grid-6 {
          grid-template-columns: 1fr;
        }

        .url-input-group {
          flex-direction: column;
          align-items: stretch;
        }

        .url-input-group label {
          min-width: auto;
        }

        .controls {
          width: 95%;
          padding: 20px;
          max-height: 90vh;
        }

        .settings-button {
          width: 45px;
          height: 45px;
          font-size: 18px;
        }

        .settings-trigger {
          width: 80px;
          height: 80px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="video-grid grid-6" id="videoGrid">
        <!-- 视频播放器将通过JavaScript动态生成 -->
      </div>
    </div>

    <script>
      class HTML5MultiPlayer {
        constructor() {
          this.players = [];
          this.playerCount = 6;
          this.init();
        }

        init() {
          this.updatePlayerCount();
          this.bindEvents();
        }

        bindEvents() {
          document.getElementById("playerCount").addEventListener("change", (e) => {
            this.playerCount = parseInt(e.target.value);
            this.updatePlayerCount();
          });
        }

        updatePlayerCount() {
          this.createUrlInputs();
          this.createVideoPlayers();
        }

        createUrlInputs() {
          const container = document.getElementById("urlInputs");
          container.innerHTML = "";

          for (let i = 0; i < this.playerCount; i++) {
            const inputGroup = document.createElement("div");
            inputGroup.className = "url-input-group";
            inputGroup.innerHTML = `
                        <label>视频${i + 1}:</label>
                        <input type="text" id="url${i}" placeholder="请输入视频流地址 (支持HLS/MP4等格式)" value="http://192.168.70.43:8999/live/test_sun/stream1/" />
                    `;
            container.appendChild(inputGroup);
          }
        }

        createVideoPlayers() {
          const container = document.getElementById("videoGrid");
          container.className = `video-grid grid-${this.playerCount}`;
          container.innerHTML = "";

          // 清理现有的播放器实例
          this.destroyAllPlayers();

          for (let i = 0; i < this.playerCount; i++) {
            const videoContainer = document.createElement("div");
            videoContainer.className = "video-container";
            // <div class="video-header">视频播放器 ${i + 1}</div>
            videoContainer.innerHTML = `
                        <div class="video-wrapper">
                            <video id="video${i}" class="video-player" controls muted></video>
                            <div class="video-status" id="status${i}">等待播放</div>
                        </div>
                    `;
            container.appendChild(videoContainer);
          }
        }

        async startPlayer(index, url) {
          if (!url || !url.trim()) {
            this.updateStatus(index, "请输入视频地址", "error");
            return;
          }

          const video = document.getElementById(`video${index}`);
          const statusEl = document.getElementById(`status${index}`);

          try {
            this.updateStatus(index, "正在加载...", "loading");

            // 停止当前播放并清理事件监听器
            this.stopPlayer(index);

            // 清理之前的事件监听器
            const newVideo = video.cloneNode(true);
            video.parentNode.replaceChild(newVideo, video);
            const freshVideo = document.getElementById(`video${index}`);

            // 尝试不同的URL格式
            let finalUrl = url.trim();

            // 如果URL不以常见的视频格式结尾，尝试添加.m3u8
            if (!finalUrl.match(/\.(m3u8|mp4|webm|ogg|flv|ts)(\?.*)?$/i)) {
              // 如果URL以/结尾，去掉斜杠再添加.m3u8
              if (finalUrl.endsWith('/')) {
                finalUrl = finalUrl.slice(0, -1);
              }
              finalUrl += '.m3u8';
            }

            console.log(`尝试播放URL: ${finalUrl}`);

            // 设置视频属性
            freshVideo.crossOrigin = "anonymous";
            freshVideo.preload = "metadata";

            // 添加事件监听器
            freshVideo.addEventListener("loadstart", () => {
              this.updateStatus(index, "开始加载...", "loading");
            });

            freshVideo.addEventListener("loadedmetadata", () => {
              this.updateStatus(index, "元数据已加载", "loading");
            });

            freshVideo.addEventListener("canplay", () => {
              statusEl.style.display = "none";
              freshVideo.play().catch((e) => {
                console.error(`播放失败 (视频${index + 1}):`, e);
                this.updateStatus(index, "播放失败: " + e.message, "error");
              });
            });

            freshVideo.addEventListener("error", (e) => {
              const error = freshVideo.error;
              let errorMessage = "播放错误";
              if (error) {
                console.error(`视频错误 (视频${index + 1}):`, error);
                switch (error.code) {
                  case error.MEDIA_ERR_ABORTED:
                    errorMessage = "播放被中止";
                    break;
                  case error.MEDIA_ERR_NETWORK:
                    errorMessage = "网络错误";
                    break;
                  case error.MEDIA_ERR_DECODE:
                    errorMessage = "解码错误";
                    break;
                  case error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                    errorMessage = "不支持的媒体格式，尝试在新标签页打开";
                    // 提供备用方案
                    setTimeout(() => {
                      this.updateStatus(index, `<a href="${finalUrl}" target="_blank" style="color: #4caf50;">点击在新窗口播放</a>`, "error");
                    }, 2000);
                    break;
                  default:
                    errorMessage = "未知错误";
                }
              }
              this.updateStatus(index, errorMessage, "error");
            });

            freshVideo.addEventListener("waiting", () => {
              this.updateStatus(index, "缓冲中...", "loading");
            });

            freshVideo.addEventListener("playing", () => {
              statusEl.style.display = "none";
            });

            freshVideo.addEventListener("progress", () => {
              this.updateStatus(index, "下载中...", "loading");
            });

            // 设置视频源并开始加载
            freshVideo.src = finalUrl;
            freshVideo.load();

          } catch (error) {
            console.error(`加载失败 (视频${index + 1}):`, error);
            this.updateStatus(index, "加载失败: " + error.message, "error");
          }
        }

        stopPlayer(index) {
          const video = document.getElementById(`video${index}`);
          if (video) {
            video.pause();
            video.removeAttribute("src");
            video.load(); // Reset the video element
          }

          this.updateStatus(index, "已停止播放", "");
        }

        updateStatus(index, message, type = "") {
          const statusEl = document.getElementById(`status${index}`);
          if (statusEl) {
            // 检查消息是否包含HTML标签
            if (message.includes('<')) {
              statusEl.innerHTML = message;
            } else {
              statusEl.textContent = message;
            }
            statusEl.className = `video-status ${type}`;
            statusEl.style.display = message ? "block" : "none";
          }
        }

        destroyAllPlayers() {
          // Stop all video players
          for (let i = 0; i < this.playerCount; i++) {
            this.stopPlayer(i);
          }
        }
      }

      // 全局实例
      const multiPlayer = new HTML5MultiPlayer();

      // 全局函数
      function startAllPlayers() {
        for (let i = 0; i < multiPlayer.playerCount; i++) {
          const url = document.getElementById(`url${i}`).value;
          if (url && url.trim()) {
            multiPlayer.startPlayer(i, url);
          }
        }
      }

      function stopAllPlayers() {
        for (let i = 0; i < multiPlayer.playerCount; i++) {
          multiPlayer.stopPlayer(i);
        }
      }

      // 控制面板显示/隐藏函数
      function showControls() {
        document.getElementById("controls").classList.add("show");
        document.getElementById("overlay").classList.add("show");
        document.body.style.overflow = "hidden";
      }

      function hideControls() {
        document.getElementById("controls").classList.remove("show");
        document.getElementById("overlay").classList.remove("show");
        document.body.style.overflow = "auto";
      }

      // ESC键关闭控制面板
      document.addEventListener("keydown", function (e) {
        if (e.key === "Escape") {
          hideControls();
        }
      });

      // 页面卸载时清理资源
      window.addEventListener("beforeunload", () => {
        multiPlayer.destroyAllPlayers();
      });
    </script>
  </body>
</html>
