<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>六路HLS视频播放器</title>
    <!-- Native HTML5 video streaming - no external libraries needed -->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: #1a1a1a;
        color: #fff;
        padding: 0;
        margin: 0;
        overflow: auto;
      }

      .container {
        margin: 0 auto;
      }

      .header {
        text-align: center;
        padding: 20px;
        background: #2d2d2d;
      }

      .header h1 {
        color: #4caf50;
        margin-bottom: 10px;
        font-size: 24px;
      }

      .header p {
        color: #ccc;
        font-size: 14px;
      }

      .video-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2px;
        padding: 10px;
        height: calc(100vh - 120px);
      }

      .video-container {
        background: #2d2d2d;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        border: 1px solid #444;
      }

      .video-header {
        background: #3d3d3d;
        padding: 8px 12px;
        font-size: 12px;
        font-weight: bold;
        text-align: center;
        border-bottom: 1px solid #555;
      }

      .video-wrapper {
        position: relative;
        width: 100%;
        height: calc((100vh - 160px) / 2);
        min-height: 200px;
      }

      .video-player {
        width: 100%;
        height: 100%;
        background: #000;
        object-fit: cover;
      }

      .video-status {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 12px;
        text-align: center;
        max-width: 80%;
      }

      .loading {
        color: #4caf50;
      }

      .error {
        color: #f44336;
      }

      .success {
        color: #4caf50;
      }

      @media (max-width: 1024px) {
        .video-grid {
          grid-template-columns: repeat(2, 1fr);
          height: calc(100vh - 120px);
        }

        .video-wrapper {
          height: calc((100vh - 160px) / 3);
        }
      }

      @media (max-width: 768px) {
        .video-grid {
          grid-template-columns: 1fr;
          height: auto;
          padding: 5px;
        }

        .video-wrapper {
          height: 250px;
        }

        .header h1 {
          font-size: 20px;
        }

        .header p {
          font-size: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>六路HLS视频播放器</h1>
      <p>自动播放 HLS 流媒体 - http://*************:8999/live/test_sun/stream1/</p>
    </div>

    <div class="container">
      <div class="video-grid" id="videoGrid">
        <div class="video-container">
          <div class="video-header">视频播放器 1</div>
          <div class="video-wrapper">
            <video id="video0" class="video-player" controls muted autoplay></video>
            <div class="video-status" id="status0">正在加载...</div>
          </div>
        </div>

        <div class="video-container">
          <div class="video-header">视频播放器 2</div>
          <div class="video-wrapper">
            <video id="video1" class="video-player" controls muted autoplay></video>
            <div class="video-status" id="status1">正在加载...</div>
          </div>
        </div>

        <div class="video-container">
          <div class="video-header">视频播放器 3</div>
          <div class="video-wrapper">
            <video id="video2" class="video-player" controls muted autoplay></video>
            <div class="video-status" id="status2">正在加载...</div>
          </div>
        </div>

        <div class="video-container">
          <div class="video-header">视频播放器 4</div>
          <div class="video-wrapper">
            <video id="video3" class="video-player" controls muted autoplay></video>
            <div class="video-status" id="status3">正在加载...</div>
          </div>
        </div>

        <div class="video-container">
          <div class="video-header">视频播放器 5</div>
          <div class="video-wrapper">
            <video id="video4" class="video-player" controls muted autoplay></video>
            <div class="video-status" id="status4">正在加载...</div>
          </div>
        </div>

        <div class="video-container">
          <div class="video-header">视频播放器 6</div>
          <div class="video-wrapper">
            <video id="video5" class="video-player" controls muted autoplay></video>
            <div class="video-status" id="status5">正在加载...</div>
          </div>
        </div>
      </div>
    </div>

    <script>
      class HTML5MultiPlayer {
        constructor() {
          this.hlsUrl = "http://*************:8999/live/test_sun/stream1/";
          this.playerCount = 6;
          this.retryAttempts = {};
          this.init();
        }

        init() {
          // 页面加载完成后自动开始播放所有视频
          this.startAllPlayers();
        }

        // 生成可能的URL变体
        generateUrlVariants(baseUrl) {
          const variants = [];
          let url = baseUrl.trim();

          // 移除末尾的斜杠
          if (url.endsWith('/')) {
            url = url.slice(0, -1);
          }

          // 原始URL
          variants.push(baseUrl.trim());

          // 添加常见的流媒体文件扩展名
          const extensions = ['.m3u8', '/playlist.m3u8', '/index.m3u8'];
          extensions.forEach(ext => {
            variants.push(url + ext);
          });

          return variants;
        }

        async startPlayer(index) {
          // 初始化重试计数
          this.retryAttempts[index] = 0;

          // 生成URL变体
          const urlVariants = this.generateUrlVariants(this.hlsUrl);

          this.tryPlayWithVariants(index, urlVariants);
        }

        async tryPlayWithVariants(index, urlVariants) {
          if (this.retryAttempts[index] >= urlVariants.length) {
            this.updateStatus(index, "所有URL格式都尝试失败", "error");
            return;
          }

          const currentUrl = urlVariants[this.retryAttempts[index]];
          const video = document.getElementById(`video${index}`);
          const statusEl = document.getElementById(`status${index}`);

          try {
            this.updateStatus(index, `正在尝试连接... (${this.retryAttempts[index] + 1}/${urlVariants.length})`, "loading");

            // 清理之前的事件监听器
            const newVideo = video.cloneNode(true);
            video.parentNode.replaceChild(newVideo, video);
            const freshVideo = document.getElementById(`video${index}`);

            console.log(`尝试播放URL (视频${index + 1}): ${currentUrl}`);

            // 设置视频属性
            freshVideo.crossOrigin = "anonymous";
            freshVideo.preload = "metadata";
            freshVideo.muted = true;
            freshVideo.autoplay = true;

            // 添加成功事件监听器
            const onSuccess = () => {
              this.updateStatus(index, "播放成功", "success");
              setTimeout(() => {
                statusEl.style.display = "none";
              }, 2000);
              console.log(`成功播放 (视频${index + 1}): ${currentUrl}`);
            };

            const onError = (e) => {
              const error = freshVideo.error;
              console.error(`视频错误 (视频${index + 1}):`, error, currentUrl);

              // 尝试下一个URL变体
              this.retryAttempts[index]++;
              setTimeout(() => {
                this.tryPlayWithVariants(index, urlVariants);
              }, 2000);
            };

            // 添加事件监听器
            freshVideo.addEventListener("loadstart", () => {
              this.updateStatus(index, "开始加载...", "loading");
            });

            freshVideo.addEventListener("loadedmetadata", () => {
              this.updateStatus(index, "元数据已加载", "loading");
            });

            freshVideo.addEventListener("canplay", onSuccess);
            freshVideo.addEventListener("error", onError);

            freshVideo.addEventListener("waiting", () => {
              this.updateStatus(index, "缓冲中...", "loading");
            });

            freshVideo.addEventListener("playing", () => {
              if (statusEl.textContent.includes("播放成功")) {
                setTimeout(() => {
                  statusEl.style.display = "none";
                }, 1000);
              }
            });

            // 设置超时机制
            const timeout = setTimeout(() => {
              console.log(`加载超时 (视频${index + 1}): ${currentUrl}`);
              this.retryAttempts[index]++;
              this.tryPlayWithVariants(index, urlVariants);
            }, 15000); // 15秒超时

            // 成功时清除超时
            freshVideo.addEventListener("canplay", () => {
              clearTimeout(timeout);
            });

            // 设置视频源并开始加载
            freshVideo.src = currentUrl;
            freshVideo.load();

          } catch (error) {
            console.error(`加载失败 (视频${index + 1}):`, error);
            this.retryAttempts[index]++;
            setTimeout(() => {
              this.tryPlayWithVariants(index, urlVariants);
            }, 2000);
          }
        }

        updateStatus(index, message, type = "") {
          const statusEl = document.getElementById(`status${index}`);
          if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = `video-status ${type}`;
            statusEl.style.display = message ? "block" : "none";
          }
        }

        startAllPlayers() {
          for (let i = 0; i < this.playerCount; i++) {
            this.startPlayer(i);
          }
        }

        stopAllPlayers() {
          for (let i = 0; i < this.playerCount; i++) {
            const video = document.getElementById(`video${i}`);
            if (video) {
              video.pause();
              video.removeAttribute("src");
              video.load();
            }
            this.updateStatus(i, "已停止播放", "");
          }
        }
      }

      // 创建播放器实例并自动开始播放
      const multiPlayer = new HTML5MultiPlayer();

      // 页面卸载时清理资源
      window.addEventListener("beforeunload", () => {
        multiPlayer.stopAllPlayers();
      });
    </script>
  </body>
</html>
